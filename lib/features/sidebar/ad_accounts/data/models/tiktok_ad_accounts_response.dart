// class TikTokAdAccountsResponse {
//   bool? success;
//   String? message;
//   List<TikTokAdAccountModel>? result;
//
//   TikTokAdAccountsResponse({this.success, this.message, this.result});
//
//   TikTokAdAccountsResponse.fromJson(Map<String, dynamic> json) {
//     success = json['success'];
//     message = json['message'];
//     if (json['result'] != null) {
//       result = <TikTokAdAccountModel>[];
//       json['result'].forEach((v) {
//         result!.add(new TikTokAdAccountModel.fromJson(v));
//       });
//     }
//   }
//
//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = new Map<String, dynamic>();
//     data['success'] = this.success;
//     data['message'] = this.message;
//     if (this.result != null) {
//       data['result'] = this.result!.map((v) => v.toJson()).toList();
//     }
//     return data;
//   }
// }
//
// class TikTokAdAccountModel {
//   String? timezone;
//   Null? brand;
//   String? country;
//   String? cellphoneNumber;
//   String? role;
//   String? telephoneNumber;
//   int? createTime;
//   String? promotionArea;
//   String? address;
//   Null? licenseUrl;
//   int? balance;
//   String? email;
//   String? advertiserId;
//   String? description;
//   String? company;
//   String? status;
//   Null? licenseProvince;
//   String? contacter;
//   String? advertiserAccountType;
//   String? industry;
//   String? currency;
//   String? licenseNo;
//   String? name;
//   Null? rejectionReason;
//   Null? promotionCenterCity;
//   Null? promotionCenterProvince;
//   String? language;
//   String? displayTimezone;
//   Null? licenseCity;
//   String? ownerBcId;
//
//   TikTokAdAccountModel(
//       {this.timezone,
//         this.brand,
//         this.country,
//         this.cellphoneNumber,
//         this.role,
//         this.telephoneNumber,
//         this.createTime,
//         this.promotionArea,
//         this.address,
//         this.licenseUrl,
//         this.balance,
//         this.email,
//         this.advertiserId,
//         this.description,
//         this.company,
//         this.status,
//         this.licenseProvince,
//         this.contacter,
//         this.advertiserAccountType,
//         this.industry,
//         this.currency,
//         this.licenseNo,
//         this.name,
//         this.rejectionReason,
//         this.promotionCenterCity,
//         this.promotionCenterProvince,
//         this.language,
//         this.displayTimezone,
//         this.licenseCity,
//         this.ownerBcId});
//
//   TikTokAdAccountModel.fromJson(Map<String, dynamic> json) {
//     timezone = json['timezone'];
//     brand = json['brand'];
//     country = json['country'];
//     cellphoneNumber = json['cellphone_number'];
//     role = json['role'];
//     telephoneNumber = json['telephone_number'];
//     createTime = json['create_time'];
//     promotionArea = json['promotion_area'];
//     address = json['address'];
//     licenseUrl = json['license_url'];
//     balance = json['balance'];
//     email = json['email'];
//     advertiserId = json['advertiser_id'];
//     description = json['description'];
//     company = json['company'];
//     status = json['status'];
//     licenseProvince = json['license_province'];
//     contacter = json['contacter'];
//     advertiserAccountType = json['advertiser_account_type'];
//     industry = json['industry'];
//     currency = json['currency'];
//     licenseNo = json['license_no'];
//     name = json['name'];
//     rejectionReason = json['rejection_reason'];
//     promotionCenterCity = json['promotion_center_city'];
//     promotionCenterProvince = json['promotion_center_province'];
//     language = json['language'];
//     displayTimezone = json['display_timezone'];
//     licenseCity = json['license_city'];
//     ownerBcId = json['owner_bc_id'];
//   }
//
//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = new Map<String, dynamic>();
//     data['timezone'] = this.timezone;
//     data['brand'] = this.brand;
//     data['country'] = this.country;
//     data['cellphone_number'] = this.cellphoneNumber;
//     data['role'] = this.role;
//     data['telephone_number'] = this.telephoneNumber;
//     data['create_time'] = this.createTime;
//     data['promotion_area'] = this.promotionArea;
//     data['address'] = this.address;
//     data['license_url'] = this.licenseUrl;
//     data['balance'] = this.balance;
//     data['email'] = this.email;
//     data['advertiser_id'] = this.advertiserId;
//     data['description'] = this.description;
//     data['company'] = this.company;
//     data['status'] = this.status;
//     data['license_province'] = this.licenseProvince;
//     data['contacter'] = this.contacter;
//     data['advertiser_account_type'] = this.advertiserAccountType;
//     data['industry'] = this.industry;
//     data['currency'] = this.currency;
//     data['license_no'] = this.licenseNo;
//     data['name'] = this.name;
//     data['rejection_reason'] = this.rejectionReason;
//     data['promotion_center_city'] = this.promotionCenterCity;
//     data['promotion_center_province'] = this.promotionCenterProvince;
//     data['language'] = this.language;
//     data['display_timezone'] = this.displayTimezone;
//     data['license_city'] = this.licenseCity;
//     data['owner_bc_id'] = this.ownerBcId;
//     return data;
//   }
// }

class TikTokAdAccountsResponse {
  bool? success;
  String? message;
  List<TikTokAdAccountModel>? result;

  TikTokAdAccountsResponse({this.success, this.message, this.result});

  TikTokAdAccountsResponse.fromJson(Map<String, dynamic> json) {
    success = json['success'];
    message = json['message'];
    if (json['result'] != null) {
      result = <TikTokAdAccountModel>[];
      json['result'].forEach((v) {
        result!.add(new TikTokAdAccountModel.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['success'] = this.success;
    data['message'] = this.message;
    if (this.result != null) {
      data['result'] = this.result!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class TikTokAdAccountModel {
  int? id;
  int? userId;
  String? accountId;
  String? type;
  String? accountName;
  String? pageId;
  String? pageAccessToken;
  Null? instagramUserId;
  Null? pageUserName;
  Null? pagePic;
  Null? instaUserName;
  Null? instaAccId;
  Null? whatsNumber;
  String? createdAt;
  String? updatedAt;
  int? status;
  Null? currency;

  TikTokAdAccountModel(
      {this.id,
        this.userId,
        this.accountId,
        this.type,
        this.accountName,
        this.pageId,
        this.pageAccessToken,
        this.instagramUserId,
        this.pageUserName,
        this.pagePic,
        this.instaUserName,
        this.instaAccId,
        this.whatsNumber,
        this.createdAt,
        this.updatedAt,
        this.status,
        this.currency});

  TikTokAdAccountModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    userId = json['user_id'];
    accountId = json['account_id'];
    type = json['type'];
    accountName = json['account_name'];
    pageId = json['page_id'];
    pageAccessToken = json['page_access_token'];
    instagramUserId = json['instagram_user_id'];
    pageUserName = json['page_user_name'];
    pagePic = json['page_pic'];
    instaUserName = json['insta_user_name'];
    instaAccId = json['insta_acc_id'];
    whatsNumber = json['whats_number'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    status = json['status'];
    currency = json['currency'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['user_id'] = this.userId;
    data['account_id'] = this.accountId;
    data['type'] = this.type;
    data['account_name'] = this.accountName;
    data['page_id'] = this.pageId;
    data['page_access_token'] = this.pageAccessToken;
    data['instagram_user_id'] = this.instagramUserId;
    data['page_user_name'] = this.pageUserName;
    data['page_pic'] = this.pagePic;
    data['insta_user_name'] = this.instaUserName;
    data['insta_acc_id'] = this.instaAccId;
    data['whats_number'] = this.whatsNumber;
    data['created_at'] = this.createdAt;
    data['updated_at'] = this.updatedAt;
    data['status'] = this.status;
    data['currency'] = this.currency;
    return data;
  }
}
