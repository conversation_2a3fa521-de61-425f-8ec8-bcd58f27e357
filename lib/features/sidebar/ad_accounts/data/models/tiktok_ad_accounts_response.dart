class TikTokAdAccountsResponse {
  bool? success;
  String? message;
  List<TikTokAdAccountModel>? result;

  TikTokAdAccountsResponse({this.success, this.message, this.result});

  TikTokAdAccountsResponse.fromJson(Map<String, dynamic> json) {
    success = json['success'];
    message = json['message'];
    if (json['result'] != null) {
      result = <TikTokAdAccountModel>[];
      json['result'].forEach((v) {
        result!.add(TikTokAdAccountModel.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['success'] = success;
    data['message'] = message;
    if (result != null) {
      data['result'] = result!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class TikTokAdAccountModel {
  String? advertiserId;
  String? advertiserName;
  String? currency;

  TikTokAdAccountModel({this.advertiserId, this.advertiserName, this.currency});

  TikTokAdAccountModel.fromJson(Map<String, dynamic> json) {
    if (json.containsKey('advertiser_id')) {
      advertiserId = json['advertiser_id'];
    }
    if (json.containsKey('currency')) {
      currency = json['currency'];
    }
    if (json.containsKey('account_id')) {
      advertiserId = json['account_id'];
    }
    if (json.containsKey('advertiser_name')) {
      advertiserName = json['advertiser_name'];
    }
    if (json.containsKey('account_name')) {
      advertiserName = json['account_name'];
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['advertiser_id'] = advertiserId;
    data['advertiser_name'] = advertiserName;
    data['currency'] = currency;
    return data;
  }
}
