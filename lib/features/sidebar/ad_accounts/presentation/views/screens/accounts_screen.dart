
import 'package:ads_dv/features/auth/data/models/user.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../../utils/di/injection.dart';
import '../../../../../../utils/hive_helper/hive_helper.dart';
import '../../../../../../utils/res/app_assets.dart';
import '../../../../../../utils/res/constants.dart';
import '../../../../../../utils/res/router/routes.dart';
import '../../../../../../widgets/appbar.dart';
import '../../../../../../widgets/cached__image.dart';
import '../../../../../../widgets/custom_text.dart';
import '../../../../../../widgets/loading_widget.dart';
import '../../../../../auth/presentation/login/controllers/facebook_disconnect/facebook_disconnect_cubit.dart';
import '../../../../../auth/presentation/login/controllers/login/login_cubit.dart';
import '../../../../payment/presentation/controllers/payment_controller/payment_cubit.dart';
import '../../../../widgets/account_widget.dart';
import '../../controllers/get_add_accounts/get_add_accounts_cubit.dart';
import '../../controllers/get_snapChat_add_accounts/get_snap_chat_add_accounts_cubit.dart';
import '../../controllers/tiktok_accounts/tiktok_accounts_cubit.dart';

class AccountsScreen extends StatefulWidget {
  const AccountsScreen({super.key});

  @override
  State<AccountsScreen> createState() => _AccountsScreenState();
}

class _AccountsScreenState extends State<AccountsScreen> {
  int? rightSelectedIndex;
  int? rightSelectedAvdertiserIndex;
  int? rightSelectedSnapChatrIndex;

  @override
  void initState() {
    super.initState();
    // Initialize async task in microtask
    Future.microtask(() async {
      final user = instance.get<HiveHelper>().getUser();
      if (user?.accessToken != null && user?.accessToken != "") {
        print(
            'User has access token: ${instance<HiveHelper>().getUser()?.tiktokToken}');
        try {
          // Call the method to check default accounts
          await TiktokAccountsCubit.get(context)
              .getDefaultTiktokAccounts(context: context);
          await GetAdAccountsCubit.get(context)
              .getDefaultAccounts(context: context);
          await GetAdAccountsCubit.get(context)
              .checkDefaultAccounts(context: context);
          await GetSnapChatAddAccountsCubit.get(context)
              .getDefaultAccounts(context: context);
          // await GetSnapChatAddAccountsCubit.get(context)
          //     .getAdAccounts(context: context);

          if (TiktokAccountsCubit.get(context).defaultTiktokAccounts?.length ==
              1) {
            instance<HiveHelper>().setAdvertiserId(
                TiktokAccountsCubit.get(context)
                    .defaultTiktokAccounts![0]
                    .advertiserId!);
            instance<HiveHelper>().setTiktokPageName(
                TiktokAccountsCubit.get(context)
                    .defaultTiktokAccounts![0]
                    .name!);
          }
          if (GetAdAccountsCubit.get(context).defaultAccounts.length == 1) {
            await GetAdAccountsCubit.get(context)
                .changeUserData(index: 0, context: context);
          }

          if (GetSnapChatAddAccountsCubit.get(context)
                  .defaultSnapChatAccounts
                  .length ==
              1) {
            instance<HiveHelper>().setSnapAdAccountId(
                GetSnapChatAddAccountsCubit.get(context)
                    .defaultSnapChatAccounts[0]
                    .accountId!);
            GetSnapChatAddAccountsCubit.get(context).setSelectedDefaultAccount(
                GetSnapChatAddAccountsCubit.get(context)
                    .defaultSnapChatAccounts[0]);
            print(
                'getSnapChatDefaultsAccount: ${instance<HiveHelper>().getSnapAdAccountId()}');
          }
        } catch (error) {
          // Handle errors if checkDefaultAccounts fails
          print('Error checking default accounts: $error');
          // Optionally, you could show a UI error message here
        }
      } else {
        await TiktokAccountsCubit.get(context)
            .getDefaultTiktokAccounts(context: context);
        await GetAdAccountsCubit.get(context)
            .getDefaultAccounts(context: context);
        await GetSnapChatAddAccountsCubit.get(context)
            .getDefaultAccounts(context: context);
        await GetAdAccountsCubit.get(context)
            .checkDefaultAccounts(context: context);
        // await GetSnapChatAddAccountsCubit.get(context)
        //     .getAdAccounts(context: context);
        print(
            'No access token found ${instance<HiveHelper>().getUser()?.accessToken}');
      }
    });
    // print(
    //     'localCheck  ${TiktokAccountsCubit.get(context).defaultTiktokAccounts?[0].advertiserId}  ${instance<HiveHelper>().getSnapAdAccountId()} ${instance<HiveHelper>().getAdvertiserId()} ${instance<HiveHelper>().getUser()?.defaultAccountId}');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: "Accounts".tr,
        showBackButton: true,
        hasDrawer: true,
      ),
      body: BlocProvider(
        create: (context) => FacebookDisconnectCubit(),
        child: MultiBlocListener(
          listeners: [
            // Listen to the Facebook disconnect state and handle side effects
            BlocListener<FacebookDisconnectCubit, FacebookDisconnectState>(
              listener: (context, state) {
                if (state is FacebookDisconnectLoading) {
                  // Show loading state if Facebook is being disconnected
                  showDialog(
                    context: context,
                    builder: (_) => const LoadingWidget(isCircle: true),
                  );
                } else if (state is FacebookDisconnected) {
                  // Show a success message or handle the success state
                  Navigator.of(context).pop(); // Pop the loading dialog
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Disconnected from Facebook'.tr)),
                  );
                } else if (state is FacebookDisconnectError) {
                  Navigator.of(context).pop(); // Pop the loading dialog
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Failed to disconnect'.tr)),
                  );
                }
              },
            ),
          ],
          child: BlocBuilder<PaymentCubit, PaymentState>(
            builder: (context, paymentState) {
              return BlocBuilder<FacebookDisconnectCubit,
                  FacebookDisconnectState>(
                builder: (context, fbState) {
                  return BlocBuilder<LoginCubit, LoginState>(
                    builder: (context, loginState) {
                      return BlocBuilder<GetAdAccountsCubit,
                          GetAdAccountsState>(
                        builder: (context, adAccountState) {
                          return BlocBuilder<TiktokAccountsCubit,
                              TiktokAccountsState>(
                            builder: (context, tikTokState) {
                              return BlocBuilder<GetSnapChatAddAccountsCubit,
                                  GetSnapChatAddAccountsState>(
                                builder: (context, snapChatState) {
                                  return SingleChildScrollView(
                                    physics: const BouncingScrollPhysics(),
                                    child: Padding(
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 24.0, horizontal: 8.0),
                                      child: Column(
                                        children: [
                                          // Meta (Facebook) Account Management
                                          _buildMetaAccountSection(
                                              context,
                                              loginState,
                                              adAccountState,
                                              fbState,
                                              paymentState),
                                          SizedBox(height: 18.h),
                                          _buildTikTokAccountSection(context,
                                              tikTokState, adAccountState),
                                          // SizedBox(height: 18.h),
                                          // // TikTok Account Management
                                          // _buildAccountWidget(
                                          //     AppAssets.tiktok, "TikTok", false),
                                          SizedBox(height: 18.h),
                                          // Snapchat Account Management
                                          _buildSnapChatSection(context,
                                              snapChatState, adAccountState),
                                          SizedBox(height: 18.h),
                                          // Google Account Management
                                          _buildAccountWidget(AppAssets.google,
                                              "Google", false),
                                        ],
                                      ),
                                    ),
                                  );
                                },
                              );
                            },
                          );
                        },
                      );
                    },
                  );
                },
              );
            },
          ),
        ),
      ),
    );
  }

  // Helper method for building the Meta Account section
  Widget _buildMetaAccountSection(
      BuildContext context,
      LoginState loginState,
      GetAdAccountsState adAccountState,
      FacebookDisconnectState fbState,
      PaymentState paymentState) {
    return Container(
      // height: 70.h,
      decoration: ShapeDecoration(
        color: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        shadows: const [
          BoxShadow(
            color: Color(0x19000000),
            blurRadius: 22,
            offset: Offset(0, 4),
            spreadRadius: 0,
          )
        ],
      ),
      child: Column(
        children: [
          Padding(
            padding:  EdgeInsets.symmetric(horizontal: 16.sp),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Padding(
                      padding: EdgeInsets.symmetric(
                          vertical: 22.sp),
                      child: const CachedImageWidget(assetsImage: AppAssets.meta),
                    ),
                    5.horizontalSpace,
                    CustomText(
                      text: "Meta".tr,
                      alignment: AlignmentDirectional.center,
                      color: Constants.primaryTextColor,
                      fontSize: 15.sp,
                      fontWeight: FontWeight.w700,
                    )
                  ],
                ),
                if (adAccountState is CheckDefaultsAccountsStateLoaded) ...[
                  if (adAccountState.checkDefaultAccountResponse?.subscribed ==
                      false) ...[
                    InkWell(
                      onTap: () {
                        Navigator.of(context).pushNamed(Routes.payment);
                      },
                      child: Container(
                        decoration: ShapeDecoration(
                          gradient: Constants.defGradient,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(38),
                          ),
                        ),
                        child: Padding(
                          padding: EdgeInsets.symmetric(
                              vertical: 8.sp, horizontal: 14.sp),
                          child: Text(
                            'buy'.tr,
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 12.sp,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ),
                      ),
                    )
                  ] else if (adAccountState
                              .checkDefaultAccountResponse?.subscribed ==
                          true &&
                      instance<HiveHelper>().getUser()?.accessToken?.isEmpty ==
                          true) ...[
                    InkWell(
                      onTap: () {
                        // final checkDefaultAccountResponse = adAccountState.checkDefaultAccountResponse;
                        // if (adAccountState.checkDefaultAccountResponse.result == true) {
                        LoginCubit.get(context).signInWithFacebook(
                            context: context, isConnect: true);
                        // }
                        // else {
                        //   Navigator.of(context).pushNamed(Routes.payment);
                        // }
                      },
                      child: Container(
                        decoration: ShapeDecoration(
                          gradient: Constants.defGradient,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(38),
                          ),
                          shadows: const [
                            BoxShadow(
                              color: Color(0x19000000),
                              blurRadius: 22,
                              offset: Offset(0, 4),
                              spreadRadius: 0,
                            ),
                          ],
                        ),
                        child: Padding(
                          padding: EdgeInsets.symmetric(
                              vertical: 8.sp, horizontal: 14.sp),
                          child: Text(
                            'Connect'.tr,
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 12.sp,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ),
                      ),
                    )
                  ] else if (adAccountState.checkDefaultAccountResponse?.result ==
                          true &&
                      adAccountState.checkDefaultAccountResponse?.subscribed ==
                          true &&
                      instance<HiveHelper>().getUser()?.accessToken?.isNotEmpty ==
                          true) ...[
                    Row(
                      children: [
                        // if (adAccountState.checkDefaultAccountResponse?.result ==
                        //     true)
                        InkWell(
                          onTap: () {
                            Navigator.pushNamed(context, Routes.metaAccounts);
                          },
                          child: Container(
                            decoration: ShapeDecoration(
                              shape: RoundedRectangleBorder(
                                side: const BorderSide(
                                    width: 0.50,
                                    color: Constants.primaryTextColor),
                                borderRadius: BorderRadius.circular(26),
                              ),
                              shadows: const [
                                BoxShadow(
                                  color: Color(0x19000000),
                                  blurRadius: 22,
                                  offset: Offset(0, 4),
                                  spreadRadius: 0,
                                )
                              ],
                            ),
                            child: Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: Text(
                                'View Account'.tr,
                                style: TextStyle(
                                  color: Constants.primaryTextColor,
                                  fontSize: 10.sp,
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                            ),
                          ),
                        ),
                        if(instance<HiveHelper>().getUser()?.accessToken?.isNotEmpty ==
                            true)...[
                          InkWell(
                            onTap: () async {
                              FacebookDisconnectCubit.get(context)
                                  .facebookDisconnect(
                                userId:
                                instance<HiveHelper>().getUserId().toString(),
                                context: context,
                              );
                              instance<HiveHelper>().deleteAdAccount();
                              instance<HiveHelper>().deleteMetaPages();
                              instance<HiveHelper>().setUserModel(
                                UserData(
                                    id: instance<HiveHelper>().getUser()?.id,
                                    name: instance<HiveHelper>().getUser()?.name,
                                    email: instance<HiveHelper>().getUser()?.email,
                                    phone: instance<HiveHelper>().getUser()?.phone,
                                    photo: instance<HiveHelper>().getUser()?.photo,
                                    pageName:
                                    instance<HiveHelper>().getUser()?.pageName,
                                    pagePic:
                                    instance<HiveHelper>().getUser()?.pagePic,
                                    emailVerifiedAt: instance<HiveHelper>()
                                        .getUser()
                                        ?.emailVerifiedAt,
                                    token: instance<HiveHelper>().getUser()?.token,
                                    accessToken: null,
                                    userId:
                                    instance<HiveHelper>().getUser()?.userId,
                                    createdAt:
                                    instance<HiveHelper>().getUser()?.createdAt,
                                    updatedAt:
                                    instance<HiveHelper>().getUser()?.updatedAt,
                                    defaultAccountId: null,
                                    defaultPageId: null,
                                    defaultPageAccessToken: null,
                                    defaultAccountName: instance<HiveHelper>()
                                        .getUser()
                                        ?.defaultAccountName,
                                    pageUserName: instance<HiveHelper>()
                                        .getUser()
                                        ?.pageUserName,
                                    instUserName: instance<HiveHelper>()
                                        .getUser()
                                        ?.instUserName,
                                    instAccId:
                                    instance<HiveHelper>().getUser()?.instAccId,
                                    whatsNumber: instance<HiveHelper>()
                                        .getUser()
                                        ?.whatsNumber,
                                    instUserId: instance<HiveHelper>()
                                        .getUser()
                                        ?.instUserId,
                                    snapChatToken: instance<HiveHelper>()
                                        .getUser()
                                        ?.snapChatToken,tiktokToken: instance<HiveHelper>().getUser()?.tiktokToken),
                              );
                              // print('defaultAccountIdxzcx1 ${instance<HiveHelper>()
                              //     .getUser()
                              //     ?.defaultAccountId}');
                              await Navigator.of(context).pushNamedAndRemoveUntil(
                                  Routes.splash, (route) => false);
                            },
                            child: Container(
                              decoration: ShapeDecoration(
                                color: Constants.redColor.withOpacity(0.1),
                                shape: RoundedRectangleBorder(
                                  side: const BorderSide(
                                      width: 0.50, color: Colors.transparent),
                                  borderRadius: BorderRadius.circular(26),
                                ),
                                shadows: const [
                                  BoxShadow(
                                    color: Color(0x19000000),
                                    blurRadius: 22,
                                    offset: Offset(0, 4),
                                    spreadRadius: 0,
                                  )
                                ],
                              ),
                              child: Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Text(
                                  'Disconnect'.tr,
                                  style: TextStyle(
                                    color: Constants.redColor,
                                    fontSize: 10.sp,
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                        // SizedBox(width: 12.w),
                        // Disconnect Button
                      ],
                    )
                  ]
                  // CustomButton(text: 'ahmed', onPressed: () {})
                ],
              ],
            ),
          ),
          // if(adAccountState is CheckDefaultsAccountsStateLoaded)
          if(instance.get<HiveHelper>().getUser()?.accessToken != "")...[
            if (GetAdAccountsCubit.get(context).defaultAccounts.isNotEmpty)
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Column(
                  children: [
                    ...List.generate(
                        GetAdAccountsCubit.get(context).defaultAccounts.length,
                            (index) {
                          return SizedBox(
                            height: 90.0.h,
                            child: Center(
                              child: Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 20.0,
                                ),
                                decoration: BoxDecoration(
                                  color: (GetAdAccountsCubit.get(context)
                                      .defaultAccounts[index]
                                      .pageId ==
                                      instance<HiveHelper>()
                                          .getUser()
                                          ?.defaultPageId) &&
                                      (GetAdAccountsCubit.get(context)
                                          .defaultAccounts[index]
                                          .accountId ==
                                          instance<HiveHelper>()
                                              .getUser()
                                              ?.defaultAccountId)
                                      ? Colors.white12
                                      : Colors.white,
                                  borderRadius: BorderRadius.circular(16.0),
                                  // Rounded edges
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withOpacity(0.1),
                                      // Light shadow
                                      // blurRadius: 6.0,
                                      offset: const Offset(0, 2), // Slight offset
                                    ),
                                  ],
                                  border: Border.all(
                                    color: GetAdAccountsCubit.get(context)
                                        .defaultAccounts[index]
                                        .accountId !=
                                        instance<HiveHelper>()
                                            .getUser()
                                            ?.defaultAccountId
                                        ? Colors.grey.withOpacity(0.3)
                                        : Colors.white12, // Border color
                                  ),
                                ),
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    // const SizedBox(height: 4.0),
                                    // Spacing between texts
                                    Row(
                                      // mainAxisAlignment: MainAxisAlignment.start,
                                      children: [
                                        Expanded(
                                          child: Column(
                                            crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                            children: [
                                              const Text(
                                                "Page",
                                                style: TextStyle(
                                                  fontSize: 18.0,
                                                  fontWeight: FontWeight.w500,
                                                  color: Colors.black,
                                                ),
                                              ),
                                              SizedBox(
                                                width: 100.0.w,
                                                child: Text(
                                                  GetAdAccountsCubit.get(context)
                                                      .defaultAccounts[index]
                                                      .pageUserName ??
                                                      "",
                                                  style: const TextStyle(
                                                    fontSize: 12.0,
                                                    fontWeight: FontWeight.normal,
                                                    color: Colors.black,
                                                  ),
                                                  textAlign: TextAlign.start,
                                                  overflow: TextOverflow.ellipsis,
                                                  maxLines: 1,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                        SizedBox(
                                          width: 25.0.w,
                                        ),
                                        // const Spacer(),
                                        Center(
                                          child: Container(
                                            height: 70.0.h,
                                            width: 1.0.w,
                                            color: Colors.white,
                                          ),
                                        ),
                                        SizedBox(
                                          width: 25.0.w,
                                        ),
                                        // const Spacer(),
                                        Expanded(
                                          flex: 2,
                                          child: Column(
                                            crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                            mainAxisSize: MainAxisSize.min,
                                            children: [
                                              const Text(
                                                "Ad Account",
                                                style: TextStyle(
                                                  fontSize: 18.0,
                                                  fontWeight: FontWeight.w500,
                                                  color: Colors.black,
                                                ),
                                                maxLines: 1,
                                              ),
                                              Text(
                                                GetAdAccountsCubit.get(context)
                                                    .defaultAccounts[index]
                                                    .accountName ??
                                                    "",
                                                style: const TextStyle(
                                                  fontSize: 12.0,
                                                  fontWeight: FontWeight.normal,
                                                  color: Colors.black,
                                                ),
                                                textAlign: TextAlign.center,
                                                overflow: TextOverflow.ellipsis,
                                                maxLines: 1,
                                              ),
                                            ],
                                          ),
                                        ),
                                        // const Spacer(),
                                        Expanded(
                                          child: SizedBox(
                                            height: 30.h,
                                            width: 30.w,
                                            child: Radio<int>(
                                              value: index,
                                              groupValue: (GetAdAccountsCubit.get(
                                                  context)
                                                  .defaultAccounts[index]
                                                  .accountId ==
                                                  instance<HiveHelper>()
                                                      .getUser()
                                                      ?.defaultAccountId) &&
                                                  (GetAdAccountsCubit.get(context)
                                                      .defaultAccounts[index]
                                                      .pageId ==
                                                      instance<HiveHelper>()
                                                          .getUser()
                                                          ?.defaultPageId)
                                                  ? index
                                                  : rightSelectedIndex,
                                              onChanged: (int? selectedIndex) async {
                                                // setState(() {
                                                rightSelectedIndex = selectedIndex;
                                                // });
                                                await GetAdAccountsCubit.get(context)
                                                    .changeUserData(
                                                    index: index, context: context);
                                                print(
                                                    'userOnChangeddsfas $rightSelectedIndex $selectedIndex');
                                                // print(
                                                //     'pageNamedfjklzfhv ${instance<HiveHelper>().getUser()?.pageName}');
                                                setState(() {});
                                              },
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          );
                        }),
                  ],
                ),
              ),
          ]


          // Stepper(
          //   currentStep: _currentStep,
          //   onStepTapped: (step) {
          //     setState(() {
          //       _currentStep = step;
          //     });
          //   },
          //   onStepContinue: () {
          //     if (_currentStep < _steps().length - 1) {
          //       setState(() {
          //         _currentStep += 1;
          //       });
          //     }
          //   },
          //   onStepCancel: () {
          //     if (_currentStep > 0) {
          //       setState(() {
          //         _currentStep -= 1;
          //       });
          //     }
          //   },
          //   steps: _steps(),
          // )
        ],
      ),
    );
  }

  Widget _buildTikTokAccountSection(
    BuildContext context,
    TiktokAccountsState tikTokState,
    GetAdAccountsState adAccountState,
  ) {
    return Container(
      decoration: ShapeDecoration(
        color: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        shadows: const [
          BoxShadow(
            color: Color(0x19000000),
            blurRadius: 22,
            offset: Offset(0, 4),
            spreadRadius: 0,
          )
        ],
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Padding(
                    padding: EdgeInsets.symmetric(
                        vertical: 22.sp, horizontal: 16.sp),
                    child:
                        const CachedImageWidget(assetsImage: AppAssets.tiktok),
                  ),
                  CustomText(
                    text: "TikTok".tr,
                    alignment: AlignmentDirectional.center,
                    color: Constants.primaryTextColor,
                    fontSize: 15.sp,
                    fontWeight: FontWeight.w700,
                  ),
                ],
              ),
              if (adAccountState is CheckDefaultsAccountsStateLoaded) ...[
                if (adAccountState.checkDefaultAccountResponse?.subscribed ==
                    false) ...[
                  InkWell(
                    onTap: () {
                      Navigator.of(context).pushNamed(Routes.payment);
                    },
                    child: _buildGradientButton("Buy"),
                  ),
                ] else if (adAccountState
                            .checkDefaultAccountResponse?.subscribed ==
                        true &&
                    (adAccountState.checkDefaultAccountResponse?.tiktokToken ==
                            null ||
                        adAccountState
                                .checkDefaultAccountResponse?.tiktokToken ==
                            "")) ...[
                  Row(
                    children: [
                      InkWell(
                        onTap: () {
                          Navigator.of(context)
                              .pushNamed(Routes.tiktokConnectionWebView);
                        },
                        child: _buildGradientButton("Connect"),
                      ),
                    ],
                  ),
                ] else if (adAccountState.checkDefaultAccountResponse?.tiktok ==
                        true &&
                    adAccountState.checkDefaultAccountResponse?.subscribed ==
                        true &&
                    (adAccountState.checkDefaultAccountResponse?.tiktokToken !=
                            null ||
                        adAccountState
                                .checkDefaultAccountResponse?.tiktokToken !=
                            "")) ...[
                  Row(
                    children: [
                      InkWell(
                        onTap: () {
                          Navigator.pushNamed(context, Routes.tiktokAccounts);
                        },
                        child: _buildOutlinedButton("View Account"),
                      ),
                    ],
                  ),
                ],
                if(instance.get<HiveHelper>().getUser()?.tiktokToken?.isNotEmpty == true || instance.get<HiveHelper>().getUser()?.tiktokToken != null)...[
                  InkWell(
                    onTap: () async {
                      await TiktokAccountsCubit.get(context)
                          .disconnect(context: context);
                      // instance<HiveHelper>().deleteSnapAdAccountId();
                      instance<HiveHelper>().setUserModel(
                        UserData(
                            id: instance<HiveHelper>().getUser()?.id,
                            name: instance<HiveHelper>().getUser()?.name,
                            email: instance<HiveHelper>().getUser()?.email,
                            phone: instance<HiveHelper>().getUser()?.phone,
                            photo: instance<HiveHelper>().getUser()?.photo,
                            pageName:
                            instance<HiveHelper>().getUser()?.pageName,
                            pagePic:
                            instance<HiveHelper>().getUser()?.pagePic,
                            emailVerifiedAt: instance<HiveHelper>()
                                .getUser()
                                ?.emailVerifiedAt,
                            token: instance<HiveHelper>().getUser()?.token,
                            accessToken: instance<HiveHelper>()
                                .getUser()
                                ?.accessToken,
                            userId:
                            instance<HiveHelper>().getUser()?.userId,
                            createdAt:
                            instance<HiveHelper>().getUser()?.createdAt,
                            updatedAt:
                            instance<HiveHelper>().getUser()?.updatedAt,
                            defaultAccountId: instance<HiveHelper>()
                                .getUser()
                                ?.defaultAccountId,
                            defaultPageId: instance<HiveHelper>()
                                .getUser()
                                ?.defaultPageId,
                            defaultPageAccessToken: instance<HiveHelper>()
                                .getUser()
                                ?.defaultPageAccessToken,
                            defaultAccountName: instance<HiveHelper>()
                                .getUser()
                                ?.defaultAccountName,
                            pageUserName: instance<HiveHelper>()
                                .getUser()
                                ?.pageUserName,
                            instUserName: instance<HiveHelper>()
                                .getUser()
                                ?.instUserName,
                            instAccId:
                            instance<HiveHelper>().getUser()?.instAccId,
                            whatsNumber: instance<HiveHelper>()
                                .getUser()
                                ?.whatsNumber,
                            instUserId: instance<HiveHelper>()
                                .getUser()
                                ?.instUserId,
                            snapChatToken: instance<HiveHelper>()
                                .getUser()?.snapChatToken,tiktokToken: null),
                      );
                      // print('defaultAccountIdxzcx1 ${instance<HiveHelper>()
                      //     .getUser()
                      //     ?.defaultAccountId}');
                      await Navigator.of(context).pushNamedAndRemoveUntil(
                          Routes.splash, (route) => false);
                    },
                    child: Container(
                      decoration: ShapeDecoration(
                        color: Constants.redColor.withOpacity(0.1),
                        shape: RoundedRectangleBorder(
                          side: const BorderSide(
                              width: 0.50, color: Colors.transparent),
                          borderRadius: BorderRadius.circular(26),
                        ),
                        shadows: const [
                          BoxShadow(
                            color: Color(0x19000000),
                            blurRadius: 22,
                            offset: Offset(0, 4),
                            spreadRadius: 0,
                          )
                        ],
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Text(
                          'Disconnect'.tr,
                          style: TextStyle(
                            color: Constants.redColor,
                            fontSize: 10.sp,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                      ),
                    ),
                  ),
                ]
              ],
            ],
          ),
          if(instance.get<HiveHelper>().getUser()?.tiktokToken != "")...[
            if (TiktokAccountsCubit.get(context)
                .defaultTiktokAccounts
                ?.isNotEmpty ==
                true)
              _buildTikTokAccountsList(context),
          ]
        ],
      ),
    );
  }

  Widget _buildSnapChatSection(
    BuildContext context,
    GetSnapChatAddAccountsState snapChatState,
    GetAdAccountsState adAccountState,
  ) {
    return Container(
      decoration: ShapeDecoration(
        color: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        shadows: const [
          BoxShadow(
            color: Color(0x19000000),
            blurRadius: 22,
            offset: Offset(0, 4),
            spreadRadius: 0,
          )
        ],
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Padding(
                    padding: EdgeInsets.symmetric(
                        vertical: 22.sp, horizontal: 16.sp),
                    child: const CachedImageWidget(assetsImage: AppAssets.snap),
                  ),
                  CustomText(
                    text: "SnapChat".tr,
                    alignment: AlignmentDirectional.center,
                    color: Constants.primaryTextColor,
                    fontSize: 15.sp,
                    fontWeight: FontWeight.w700,
                  ),
                ],
              ),
              if (adAccountState is CheckDefaultsAccountsStateLoaded) ...[
                if (adAccountState.checkDefaultAccountResponse?.subscribed ==
                    false) ...[
                  InkWell(
                    onTap: () {
                      Navigator.of(context).pushNamed(Routes.payment);
                    },
                    child: _buildGradientButton("Buy"),
                  ),
                ] else if (adAccountState
                            .checkDefaultAccountResponse?.subscribed ==
                        true &&
                    (adAccountState.checkDefaultAccountResponse?.snapChatToken ==
                            null ||
                        adAccountState
                                .checkDefaultAccountResponse?.snapChatToken ==
                            "")) ...[
                  InkWell(
                    onTap: () {
                      Navigator.of(context)
                          .pushNamed(Routes.snapChatConnectionWebView);
                    },
                    child: _buildGradientButton("Connect"),
                  ),
                ] else if (adAccountState
                            .checkDefaultAccountResponse?.snapChat ==
                        true &&
                    adAccountState.checkDefaultAccountResponse?.subscribed ==
                        true &&
                    (adAccountState
                                .checkDefaultAccountResponse?.snapChatToken !=
                            null ||
                        adAccountState
                                .checkDefaultAccountResponse?.snapChatToken !=
                            "")) ...[
                  Row(
                    children: [
                      InkWell(
                        onTap: () {
                          Navigator.pushNamed(
                              context, Routes.snapChatAccountsScreen);
                        },
                        child: _buildOutlinedButton("View Account"),
                      ),
                    ],
                  ),
                ],
                if(instance.get<HiveHelper>().getUser()?.snapChatToken?.isNotEmpty == true || instance.get<HiveHelper>().getUser()?.snapChatToken != null)...[
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8.0),
                    child: InkWell(
                      onTap: () async {
                        await GetSnapChatAddAccountsCubit.get(context)
                            .disconnect(context: context);
                        // instance<HiveHelper>().deleteSnapAdAccountId();
                        instance<HiveHelper>().setUserModel(
                          UserData(
                              id: instance<HiveHelper>().getUser()?.id,
                              name: instance<HiveHelper>().getUser()?.name,
                              email: instance<HiveHelper>().getUser()?.email,
                              phone: instance<HiveHelper>().getUser()?.phone,
                              photo: instance<HiveHelper>().getUser()?.photo,
                              pageName:
                              instance<HiveHelper>().getUser()?.pageName,
                              pagePic:
                              instance<HiveHelper>().getUser()?.pagePic,
                              emailVerifiedAt: instance<HiveHelper>()
                                  .getUser()
                                  ?.emailVerifiedAt,
                              token: instance<HiveHelper>().getUser()?.token,
                              accessToken: instance<HiveHelper>()
                                  .getUser()
                                  ?.accessToken,
                              userId:
                              instance<HiveHelper>().getUser()?.userId,
                              createdAt:
                              instance<HiveHelper>().getUser()?.createdAt,
                              updatedAt:
                              instance<HiveHelper>().getUser()?.updatedAt,
                              defaultAccountId: instance<HiveHelper>()
                                  .getUser()
                                  ?.defaultAccountId,
                              defaultPageId: instance<HiveHelper>()
                                  .getUser()
                                  ?.defaultPageId,
                              defaultPageAccessToken: instance<HiveHelper>()
                                  .getUser()
                                  ?.defaultPageAccessToken,
                              defaultAccountName: instance<HiveHelper>()
                                  .getUser()
                                  ?.defaultAccountName,
                              pageUserName: instance<HiveHelper>()
                                  .getUser()
                                  ?.pageUserName,
                              instUserName: instance<HiveHelper>()
                                  .getUser()
                                  ?.instUserName,
                              instAccId:
                              instance<HiveHelper>().getUser()?.instAccId,
                              whatsNumber: instance<HiveHelper>()
                                  .getUser()
                                  ?.whatsNumber,
                              instUserId: instance<HiveHelper>()
                                  .getUser()
                                  ?.instUserId,
                              snapChatToken: null,tiktokToken: instance<HiveHelper>()
                              .getUser()?.tiktokToken),
                        );
                        // print('defaultAccountIdxzcx1 ${instance<HiveHelper>()
                        //     .getUser()
                        //     ?.defaultAccountId}');
                        await Navigator.of(context).pushNamedAndRemoveUntil(
                            Routes.splash, (route) => false);
                      },
                      child: Container(
                        decoration: ShapeDecoration(
                          color: Constants.redColor.withOpacity(0.1),
                          shape: RoundedRectangleBorder(
                            side: const BorderSide(
                                width: 0.50, color: Colors.transparent),
                            borderRadius: BorderRadius.circular(26),
                          ),
                          shadows: const [
                            BoxShadow(
                              color: Color(0x19000000),
                              blurRadius: 22,
                              offset: Offset(0, 4),
                              spreadRadius: 0,
                            )
                          ],
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Text(
                            'Disconnect'.tr,
                            style: TextStyle(
                              color: Constants.redColor,
                              fontSize: 10.sp,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ],
            ],
          ),
          if(instance.get<HiveHelper>().getUser()?.snapChatToken != "")...[
            if (GetSnapChatAddAccountsCubit.get(context)
                .defaultSnapChatAccounts
                .isNotEmpty)
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Column(
                  children: [
                    ...List.generate(
                        GetSnapChatAddAccountsCubit.get(context)
                            .defaultSnapChatAccounts
                            .length ??
                            0, (index) {
                      return SizedBox(
                        height: 90.0.h,
                        child: Center(
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 20.0, vertical: 12.0),
                            decoration: BoxDecoration(
                              color: (GetSnapChatAddAccountsCubit.get(context)
                                  .defaultSnapChatAccounts[index]
                                  .accountId !=
                                  instance
                                      .get<HiveHelper>()
                                      .getSnapAdAccountId())
                                  ? Colors.white
                                  : Colors.white12,
                              borderRadius: BorderRadius.circular(16.0),
                              // Rounded edges
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.1),
                                  // Light shadow
                                  // blurRadius: 6.0,
                                  offset: const Offset(0, 2), // Slight offset
                                ),
                              ],
                              border: Border.all(
                                color: GetSnapChatAddAccountsCubit.get(context)
                                    .defaultSnapChatAccounts[index]
                                    .accountId !=
                                    instance
                                        .get<HiveHelper>()
                                        .getSnapAdAccountId()
                                    ? Colors.grey.withOpacity(0.3)
                                    : Colors.white12, // Border color
                              ),
                            ),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const SizedBox(height: 4.0),
                                // Spacing between texts
                                Row(
                                  children: [
                                    Row(
                                      // crossAxisAlignment:
                                      //     CrossAxisAlignment.start,
                                      children: [
                                        const Text(
                                          "Ad Account: ",
                                          style: TextStyle(
                                            fontSize: 18.0,
                                            fontWeight: FontWeight.w500,
                                            color: Colors.black,
                                          ),
                                        ),
                                        Text(
                                          GetSnapChatAddAccountsCubit.get(context)
                                              .defaultSnapChatAccounts[index]
                                              .accountName ??
                                              "",
                                          style: const TextStyle(
                                            fontSize: 15.0,
                                            fontWeight: FontWeight.normal,
                                            color: Colors.black,
                                          ),
                                          textAlign: TextAlign.center,
                                          overflow: TextOverflow.ellipsis,
                                          maxLines: 2,
                                        ),
                                      ],
                                    ),
                                    const Spacer(),
                                    SizedBox(
                                      width: 10.0.w,
                                    ),
                                    SizedBox(
                                      height: 30.h,
                                      width: 30.w,
                                      child: Radio<int>(
                                        value: index,
                                        groupValue:
                                        (GetSnapChatAddAccountsCubit.get(
                                            context)
                                            .defaultSnapChatAccounts[
                                        index]
                                            .accountId ==
                                            instance<HiveHelper>()
                                                .getSnapAdAccountId())
                                        // &&
                                        //     (GetAdAccountsCubit.get(
                                        //                 context)
                                        //             .defaultAccounts[
                                        //                 index]
                                        //             .pageId ==
                                        //         instance<HiveHelper>()
                                        //             .getUser()
                                        //             ?.defaultPageId)
                                            ? index
                                            : rightSelectedSnapChatrIndex,
                                        onChanged: (int? selectedIndex) async {
                                          print('userOnChangeddsfas');
                                          // setState(() {
                                          rightSelectedSnapChatrIndex =
                                              selectedIndex;
                                          instance<HiveHelper>()
                                              .setSnapAdAccountId(
                                              GetSnapChatAddAccountsCubit.get(
                                                  context)
                                                  .defaultSnapChatAccounts[
                                              selectedIndex!]
                                                  .accountId!);
                                          GetSnapChatAddAccountsCubit.get(context)
                                              .setSelectedDefaultAccount(
                                              GetSnapChatAddAccountsCubit.get(
                                                  context)
                                                  .defaultSnapChatAccounts[
                                              selectedIndex]);
                                          // instance<HiveHelper>().setTiktokPageName(
                                          //     TiktokAccountsCubit.get(context)
                                          //         .defaultTiktokAccounts![selectedIndex]
                                          //         .advertiserName!);
                                          // });
                                          // await GetAdAccountsCubit.get(
                                          //         context)
                                          //     .changeUserData(
                                          //         index: index,
                                          //         context: context);
                                          print(
                                              'snapAdAccountId ${instance<HiveHelper>().getSnapAdAccountId()}');
                                          setState(() {});
                                        },
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ),
                      );
                    }),
                  ],
                ),
              )
          ]
        ],
      ),
    );
  }

  Widget _buildGradientButton(String text) {
    return Container(
      decoration: ShapeDecoration(
        gradient: Constants.defGradient,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(38),
        ),
      ),
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 8.sp, horizontal: 14.sp),
        child: Text(
          text.tr,
          style: TextStyle(
            color: Colors.white,
            fontSize: 12.sp,
            fontWeight: FontWeight.w400,
          ),
        ),
      ),
    );
  }

  Widget _buildOutlinedButton(String text) {
    return Container(
      decoration: ShapeDecoration(
        shape: RoundedRectangleBorder(
          side:
              const BorderSide(width: 0.50, color: Constants.primaryTextColor),
          borderRadius: BorderRadius.circular(26),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Text(
          text.tr,
          style: TextStyle(
            color: Constants.primaryTextColor,
            fontSize: 10.sp,
            fontWeight: FontWeight.w400,
          ),
        ),
      ),
    );
  }

  Widget _buildTikTokAccountsList(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Column(
        children: [
          ...List.generate(
              TiktokAccountsCubit.get(context).defaultTiktokAccounts?.length ??
                  0, (index) {
            return SizedBox(
              height: 90.0.h,
              child: Center(
                child: Container(
                  padding: const EdgeInsets.symmetric(
                      horizontal: 20.0, vertical: 12.0),
                  decoration: BoxDecoration(
                    color: TiktokAccountsCubit.get(context)
                                .defaultTiktokAccounts?[index]
                                .advertiserId !=
                            instance.get<HiveHelper>().getAdvertiserId()
                        ? Colors.white
                        : Colors.white12,
                    borderRadius: BorderRadius.circular(16.0),
                    // Rounded edges
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        // Light shadow
                        // blurRadius: 6.0,
                        offset: const Offset(0, 2), // Slight offset
                      ),
                    ],
                    border: Border.all(
                      color: TiktokAccountsCubit.get(context)
                                  .defaultTiktokAccounts![index]
                                  .advertiserId !=
                              instance.get<HiveHelper>().getAdvertiserId()
                          ? Colors.grey.withOpacity(0.3)
                          : Colors.white12, // Border color
                    ),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // const SizedBox(height: 4.0),
                      // Spacing between texts
                      Row(
                        // mainAxisAlignment:
                        //     MainAxisAlignment.spaceBetween,
                        children: [
                          // const Spacer(),
                          Row(
                            // crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                "Ad Account: ",
                                style: TextStyle(
                                  fontSize: 18.0,
                                  fontWeight: FontWeight.w500,
                                  color: Colors.black,
                                ),
                              ),
                              Text(
                                TiktokAccountsCubit.get(context)
                                        .defaultTiktokAccounts?[index]
                                        .name ??
                                    "",
                                style: const TextStyle(
                                  fontSize: 15.0,
                                  fontWeight: FontWeight.normal,
                                  color: Colors.black,
                                ),
                                // textAlign: TextAlign.center,
                                overflow: TextOverflow.ellipsis,
                                maxLines: 2,
                              ),
                            ],
                          ),
                          const Spacer(),
                          // SizedBox(
                          //   width: 10.0.w,
                          // ),
                          SizedBox(
                            height: 30.h,
                            width: 30.w,
                            child: Radio<int>(
                              value: index,
                              groupValue: (TiktokAccountsCubit.get(context)
                                          .defaultTiktokAccounts![index]
                                          .advertiserId ==
                                      instance<HiveHelper>().getAdvertiserId())
                                  // &&
                                  //     (GetAdAccountsCubit.get(
                                  //                 context)
                                  //             .defaultAccounts[
                                  //                 index]
                                  //             .pageId ==
                                  //         instance<HiveHelper>()
                                  //             .getUser()
                                  //             ?.defaultPageId)
                                  ? index
                                  : rightSelectedAvdertiserIndex,
                              onChanged: (int? selectedIndex) async {
                                print('userOnChangeddsfas');
                                // setState(() {
                                rightSelectedAvdertiserIndex = selectedIndex;
                                instance<HiveHelper>().setAdvertiserId(
                                    TiktokAccountsCubit.get(context)
                                        .defaultTiktokAccounts![selectedIndex!]
                                        .advertiserId!);
                                instance<HiveHelper>().setTiktokPageName(
                                    TiktokAccountsCubit.get(context)
                                        .defaultTiktokAccounts![selectedIndex]
                                        .name!);
                                // });
                                // await GetAdAccountsCubit.get(
                                //         context)
                                //     .changeUserData(
                                //         index: index,
                                //         context: context);
                                print(
                                    'advertiserId ${instance<HiveHelper>().getAdvertiserId()}');
                                setState(() {});
                              },
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            );
          }),
        ],
      ),
    );
  }

  // Helper method for building account widgets (e.g., TikTok, Snapchat)
  Widget _buildAccountWidget(String icon, String title, bool isConnected) {
    return AccountWidget(
      icon: icon,
      title: title,
      isConnected: isConnected,
    );
  }
}
