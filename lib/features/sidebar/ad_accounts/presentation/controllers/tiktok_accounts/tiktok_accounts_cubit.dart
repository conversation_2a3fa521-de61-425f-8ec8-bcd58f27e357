import 'package:ads_dv/features/sidebar/ad_accounts/data/repos/tikTok_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../../utils/di/injection.dart';
import '../../../../../../utils/network/failure_helper.dart';
import '../../../data/models/tiktok_ad_accounts_response.dart';

part 'tiktok_accounts_state.dart';

class TiktokAccountsCubit extends Cubit<TiktokAccountsState> {
  TiktokAccountsCubit() : super(TiktokAccountsInitial());

  static TiktokAccountsCubit get(context) => BlocProvider.of(context);

  bool isConnected = false;

  bool isAccountSelected = false;
  bool isPageSelected = true;

  List<TikTokAdAccountModel>? adAccounts;
  List<TikTokAdAccountModel>? addedAdAccounts;
  List<TikTokAdAccountModel>? defaultTiktokAccounts;
  TikTokAdAccountModel? addedAdAccount;

  Future<void> getTikTokAdAccounts({
    required BuildContext context,
  }) async {
    emit(GetTikTokAdAccountsStateLoading());
    instance<TikTokRepo>().getAdAccounts().then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        print('tiktokAccountsFail ${l.message}');
        emit(GetTikTokAdAccountsStateError(l.message));
      }, (r) async {
        print('thlayer $r');
        adAccounts = r.result;
        // await setSelectedUrl(r.next);
        // await checkDefaultAccounts(context: context);
        emit(GetTikTokAdAccountsStateLoaded(r.result));
      });
    });
  }

  Future<void> disconnect({
    required BuildContext context,
  }) async {
    emit(GetTikTokAdAccountsStateLoading());
    instance<TikTokRepo>().disconnect().then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        print('getSnapChatAdAccountsResponseass ${l.message}');
        emit(GetTikTokAdAccountsStateError(l.message));
      }, (r) async {
        // adAccounts = r.result ?? [];
        // await setSelectedUrl(r.next);
        // await checkDefaultAccounts(context: context);
        // snapChatAdAccounts = r.result ?? [];
        // print(
        //     'getSnapChatAdAccountsResponseass ${r.result?.first.adaccount?.toJson()}');
        emit(UpdateStatus());
      });
    });
  }

  Future<void> getDefaultTiktokAccounts({required BuildContext context}) async {
    try {
      // emit(GetAdAccountsStateLoading());
      await instance<TikTokRepo>()
          .getDefaultTiktokAccounts()
          .then((value) => value.fold((l) {
                print('getTiktokErrorAccountsxczfjd ${l.message}');
              }, (r) async {
                defaultTiktokAccounts = r.result ?? [];
                // defaultAccounts = r.data ?? [];
                print(
                    'getTiktokAddAccountsxczfjd ${r.result?.first.accountName}');
                // await changeUserData(index: 0, context: context);
                // print('getAddAccountErrordfzxcx ${r.data?.first.pagePic}');

                emit(GetTikTokAdAccountsStateLoaded(r.result));
              }));

      // result.fold(
      //   (l) {
      //     FailureHelper.instance.handleFailures(l, context);
      //     emit(GetAdAccountsStateError(l.message));
      //   },
      //   (r) {
      //     defaultAccounts = r.data ?? [];
      //     print('getAddAccountErrordfzxcx ${r.data?.first.pagePic}');
      //
      //     emit(CheckDefaultsAccountsStateLoaded(data: r.data ?? []));
      //   },
      // );
    } catch (e) {
      print('Error while fetching default accounts: $e');
      emit(GetTikTokAdAccountsStateError(e.toString()));
    }
  }

  changeConnectionStatus(bool status) {
    isConnected = status;
    emit(UpdateStatus());
  }

  setSelectedAccount(bool status, TikTokAdAccountModel addedAdAcc) {
    isAccountSelected = status;
    // addedAdAccounts?.add(addedAdAcc);
    addedAdAccount = addedAdAcc;
    // emit(GetTikTokAdAccountsAdded(addedAdAccounts));
  }

  setSelectedPage(bool status) {
    isPageSelected = status;
    emit(UpdateStatus());
  }
}
