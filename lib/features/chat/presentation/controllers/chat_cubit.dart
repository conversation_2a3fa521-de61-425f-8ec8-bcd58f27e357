import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:pusher_channels_flutter/pusher_channels_flutter.dart';

import '../../../../utils/di/injection.dart';
import '../../../../utils/hive_helper/hive_helper.dart';
import '../../../../utils/network/failure_helper.dart';
import '../../models/chat_bot_model.dart';
import '../../repos/chat_repository.dart';

part 'chat_state.dart';

class ChatCubit extends Cubit<ChatState> {
  ChatCubit() : super(ChatInitial());

  static ChatCubit get(context) => BlocProvider.of(context);

  final String appKey = '73fcb2d5b3e2509529c7';
  final String appCluster = 'eu';
  String? channelName;
  String? receivedMsg = '';

  // Pusher instance
  final PusherChannelsFlutter pusher = PusherChannelsFlutter.getInstance();

  // Text controller for typing messages
  final TextEditingController _controller = TextEditingController();

  // List to store the chat messages
  List<Map<String, String>> messages = [];
  List<Result> chatBotMessages = [];
  final ScrollController scrollController = ScrollController();
  bool isBack = false;
  bool? isConnected = false;

  // Initialize Pusher and configure
  Future<void> pusherConfiguration(BuildContext context) async {
    try {
      isConnected = true;
      await createChat(context);
      if (channelName == null) {
        print("Error: Channel name is null or empty. $channelName");
        return;
      }

      await pusher.init(
        apiKey: appKey,
        cluster: appCluster,
        onConnectionStateChange: onConnectionStateChange,
        onError: onErrorr,
        onSubscriptionSucceeded: onSubscriptionSucceeded,
        onEvent: onEvent,
        onSubscriptionError: onSubscriptionError,
      );

      await pusher.subscribe(channelName: channelName!);
      await pusher.connect();
      emit(ChatChannelNameState(channelName!));
      print("Pusher connected, subscribed to: $channelName");
    } catch (e) {
      print("Pusher initialization error: $e");
    }
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (scrollController.hasClients) {
        scrollController.animateTo(
          scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  // Disconnect Pusher
  Future<void> disconnect() async {
    channelName = "";
    await pusher.disconnect();
    // isConnected = false;
    print("Disconnected from Pusher. $channelName");
  }

  // void toggleIsBot(bool value) {
  //   isBot = value;
  //   // emit(ChatToggleBotState(isBot));
  // }

  // Send a message
  Future<void> sendMessage(
      BuildContext context, Map<String, dynamic> message) async {
    // Simulate adding the message to the chat
    // messages.first.clear();
    // emit(ChatMessagesState(messages));
    messages.add({
      'sender': message['sender'],
      // 'customer',
      'message': message['message'],
      'created_at': message['created_at'],
    });
    // messages.add({
    //   'sender': message['sender'],
    //   // 'customer',
    //   'message': message['message'],
    // });
    if(isConnected == true){
      await instance.get<ChatRepo>().sendMessage(message);
    }


    print('messagessdas $messages');
    _scrollToBottom();
    // Emit the updated list of messages
    emit(ChatMessagesState(messages));

    print('chatMsg2 $messages');
    // Call API or repository to actually send the message
  }

  Future<void> getBotMessages({List<int>? ecxept}) async {
    // print('messagessdas ${messages}');
    // Emit the updated list of messages
    instance.get<ChatRepo>().getBotMessages().then((value) {
      value.fold(
        (l) {
          print('botMessageserror ${l.message}');
        },
        (r) {
          chatBotMessages = r.result ?? [];
          print('botMessages ${chatBotMessages.first.question}');
          emit(ChatBotMessagesState(chatBotMessages));
        },
      );
    });
  }

  // initChat() {
  //   messages.clear();
  // }

  // clearMessages() {
  //   emit(ChatMessagesState(const []));
  // }

  // Handle received events (simulate receiving a message)
  void onEvent(PusherEvent event) {
    print("Event received: $event");
    Map<String, dynamic> jsonMap = jsonDecode(event.data);
    var message = jsonMap['message']['message'];
    var sender = jsonMap['message']['sender'];
    var time = jsonMap['message']['created_at'];
    // '2025-05-14T13:52:36.000000Z'
    DateTime dateTime = DateTime.parse(time);

    String timeString =
        "${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}";

    print(timeString);
    print("Message received: $message");

    // if (jsonMap['message']['sender'] == 'admin') {
    // Simulate adding the received message to the chat
    print('chatMsg2 $messages');
    if (instance.get<HiveHelper>().getUser()?.name == sender) {
      return;
    } else {
      messages.add({
        'sender': 'admin',
        'message': message,
        'created_at': timeString,
      });
    }

    _scrollToBottom();
    // Emit the updated list of messages
    emit(ChatMessagesState(messages));
    // }
  }

  // Handle connection state changes
  void onConnectionStateChange(String? currentState, String? previousState) {
    print(
        "Connection state change: currentState=$currentState, previousState=$previousState");
  }

  // Handle subscription success
  void onSubscriptionSucceeded(String channelName, dynamic data) {
    print("Subscription succeeded on channel: $channelName, data: $data");
  }

  // Handle subscription errors
  void onSubscriptionError(String message, dynamic e) {
    print("Subscription error: $message, Exception: $e");
  }

  // Handle errors
  void onErrorr(String message, int? code, dynamic e) {
    print("Error: $message, Code: $code, Exception: $e");
  }

  // Create a chat channel (example logic)
  Future<void> createChat(BuildContext context) async {
    await instance<ChatRepo>().getChannelName().then((value) {
      value.fold((l) {
        print('Error retrieving channel name: ${l.message}');
        FailureHelper.instance.handleFailures(l, context);
      }, (r) {
        channelName = r;
        print('Successfully retrieved channel name: $channelName');
      });
    });

    if (channelName == null || channelName!.isEmpty) {
      print("Error: Channel name is null or empty.");
    } else {
      print("Channel name is valid: $channelName");
    }
  }
}
