import 'package:ads_dv/features/chat/models/chat_bot_model.dart';
import 'package:ads_dv/utils/di/injection.dart';
import 'package:ads_dv/utils/hive_helper/hive_helper.dart';
import 'package:ads_dv/utils/res/colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get/get_utils/src/extensions/internacionalization.dart';

import '../../../../utils/res/constants.dart';
import '../../../../utils/res/media_query_config.dart';
import '../../../../utils/res/router/routes.dart';
import '../../../../widgets/custom_button.dart';
import '../controllers/chat_cubit.dart';

class ChatScreen extends StatefulWidget {
  const ChatScreen({super.key});

  @override
  _ChatScreenState createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> {
  List<Map<String, String>> botDynamicMessages = [
    {
      'sender': 'admin',
      'message':
      'Hello ${instance
          .get<HiveHelper>()
          .getUser()
          ?.name}, we’re here to solve your problems, how can we help?',
    },
  ];

  bool isBot = true;
  bool isAnimationVisible = true;

  final TextEditingController _controller = TextEditingController();
  FocusNode keyNode = FocusNode();

  bool isQuesPreview = true;
  bool isBotAnswerLoading = false;

  @override
  void dispose() {
    _controller.dispose();
    ChatCubit
        .get(context)
        .scrollController
        .dispose();
    // isConnected = false;
    keyNode.unfocus();
    super.dispose();
  }

  Future<void> _sendMessage({Map<String, dynamic>? message}) async {
    DateTime dateTime = DateTime.now();

    String timeString =
        "${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}";
    Map<String, dynamic> chatMessage = {
      'sender':
      _controller.text
          .trim()
          .isNotEmpty ? 'customer' : message?['sender'],
      'message': message?['message'] ?? _controller.text.trim(),
      'created_at': timeString,
      // if (_controller.text.isNotEmpty)
      // 'senderUserName': instance.get<HiveHelper>().getUser()?.name
    };
    // message ?? _controller.text.trim();
    final chatCubit = context.read<ChatCubit>();
    if (chatMessage != {}) {
      // if (!chatCubit.isConnected!) {
      //   await chatCubit.pusherConfiguration(context);
      // }
      print('chatMsg1 $chatMessage');
      await chatCubit.sendMessage(context, chatMessage);
      isAnimationVisible = true;
      _controller.clear();
      // if (message == null) {
      //   _scrollToBottom();
      // }
    }
  }

  Future<void> _showExitDialog() async {
    return showDialog(
      context: context,
      builder: (c) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15.0),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'The conversation will be ended.'.tr,
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 20.h),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CustomButton(
                      text: 'Confirm'.tr,
                      radius: 20.0,
                      onPressed: () async {
                        await context.read<ChatCubit>().disconnect();
                        // context.read<ChatCubit>().toggleIsBot(true);
                        isBot = true;
                        isQuesPreview = true;
                        ChatCubit
                            .get(context)
                            .isConnected = false;
                        ChatCubit
                            .get(context)
                            .isBack = false;
                        // isBot = true;
                        setState(() {});
                        Navigator.of(context).pushNamedAndRemoveUntil(
                          Routes.home,
                              (route) => false,
                        );
                      },
                      color: Colors.white,
                      textColor: Constants.redColor,
                      side: const BorderSide(color: Constants.redColor),
                    ),
                    SizedBox(width: 10.w),
                    CustomButton(
                      text: 'Cancel'.tr,
                      radius: 20.0,
                      onPressed: () => Navigator.pop(context),
                      color: Colors.white,
                      side: const BorderSide(color: Constants.greenColor),
                      textColor: Constants.greenColor,
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  @override
  void initState() {
    super.initState();
    print(
        'isChatStatusChanged ${ChatCubit
            .get(context)
            .isBack} ${ChatCubit
            .get(context)
            .isConnected}');
    if (ChatCubit
        .get(context)
        .isBack == true &&
        ChatCubit
            .get(context)
            .isConnected == true) {
      isBot = false;
      isQuesPreview = false;
      setState(() {});
    }
    // else if (ChatCubit.get(context).isBack == true && isBot == false) {
    //   isBot = false;
    //   isQuesPreview = false;
    //   setState(() {});
    // }
    Future.microtask(() async {
      if (context
          .read<ChatCubit>()
          .messages
          .where((element) =>
      element['message'] == botDynamicMessages.first['message'])
          .isEmpty) {
        await _sendMessage(message: {
          'sender': 'admin',
          "message": botDynamicMessages.first['message'],
        });
        await instance.get<ChatCubit>().getBotMessages();
        setState(() {});
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text("Chat".tr),
        centerTitle: true,
        actions: [
          ChatCubit
              .get(context)
              .isConnected == true ?
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: CustomButton(
              // color: Constants.redColor,
              radius: 8.0,
              onPressed: _showExitDialog,
              text: 'endChat'.tr,
              color: Colors.transparent,
              textColor: Constants.redColor,
              side: const BorderSide(color: Constants.redColor),
            ),
          ) : const SizedBox(),
        ],
        leading: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8.0),
          child: InkWell(
            onTap: () async {
              ChatCubit
                  .get(context)
                  .isBack = true;
              setState(() {});
              print('isChatStatusChanged $isBot $isQuesPreview');
              await Navigator.pushNamedAndRemoveUntil(
                  context, Routes.home, (route) => false);
            },
            child:Icon(Icons.arrow_back_ios,size: 20.sp,),
          ),
        ),
        leadingWidth: 70.w,
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8.0),
        child: Column(
          children: [
            SizedBox(height: 10.h),
            Expanded(
              child: BlocBuilder<ChatCubit, ChatState>(
                builder: (context, state) {
                  if (state is ChatMessagesState) {
                    return Column(
                      children: [
                        // 30.verticalSpace,
                        Expanded(
                          child: ListView.builder(
                            controller: ChatCubit
                                .get(context)
                                .scrollController,
                            itemCount: state.messages.length,
                            itemBuilder: (ctx, index) {
                              final message = state.messages[index];
                              final int lastIndex = state.messages.length - 1;
                              final bool isLastMessage = index == lastIndex;
                              print('lastIndexxx $isLastMessage');
                              return Column(
                                children: [
                                  if (message['sender'] == 'endAnswer' &&
                                      message['message'] ==
                                          'Great! we’re happy to help, is there any questions else?')
                                    Align(
                                      alignment: Alignment.centerRight,
                                      child: Image.asset(
                                        'assets/images/Facebook Like (1).png',
                                        height: 200,
                                        width: 200,
                                      ),
                                    ),
                                  if (message['sender'] == 'endAnswer' &&
                                      message['message'] ==
                                          'Sorry! Where’s your problem?')
                                    Align(
                                      alignment: Alignment.centerRight,
                                      child: Image.asset(
                                        'assets/images/Thumbs Down (1).png',
                                        height: 200,
                                        width: 200,
                                      ),
                                    ),
                                  Padding(
                                    padding: EdgeInsets.only(
                                        right: message['sender'] == 'answer' ||
                                            message['sender'] ==
                                                'endAnswer' ||
                                            message['sender'] == 'admin'
                                            ? 80.0
                                            : 0.0,
                                        left: message['sender'] == 'answer' ||
                                            message['sender'] ==
                                                'endAnswer' ||
                                            message['sender'] == 'admin'
                                            ? 0.0
                                            : 80.0),
                                    child: Align(
                                      alignment: message['sender'] == 'answer' ||
                                          message['sender'] == 'endAnswer' ||
                                          message['sender'] == 'admin'
                                          ? Alignment.centerLeft
                                          : Alignment.centerRight,
                                      child: Row(
                                        mainAxisAlignment:
                                        message['sender'] == 'answer' ||
                                            message['sender'] ==
                                                'endAnswer' ||
                                            message['sender'] == 'admin'
                                            ? MainAxisAlignment.start
                                            : MainAxisAlignment.end,
                                        crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                        children: [
                                          if (message['sender'] == 'answer' ||
                                              message['sender'] == 'endAnswer' ||
                                              message['sender'] == 'admin') ...[
                                            const CircleAvatar(),
                                            const SizedBox(width: 8),
                                          ],
                                          Flexible(
                                            child: Column(
                                              crossAxisAlignment:
                                              message['sender'] == 'answer' ||
                                                  message['sender'] ==
                                                      'endAnswer' ||
                                                  message['sender'] ==
                                                      'admin'
                                                  ? CrossAxisAlignment.start
                                                  : CrossAxisAlignment.end,
                                              children: [
                                                if (message['sender'] ==
                                                    'answer' ||
                                                    message['sender'] ==
                                                        'endAnswer' ||
                                                    message['sender'] == 'admin')
                                                  const Padding(
                                                    padding: EdgeInsets.symmetric(horizontal: 8.0),
                                                    child: Text(
                                                      'Dv Ads',
                                                      style: TextStyle(
                                                          fontWeight:
                                                          FontWeight.bold),
                                                    ),
                                                  ),
                                                Container(
                                                  margin:
                                                  const EdgeInsets.symmetric(
                                                      vertical: 5,
                                                      horizontal: 10),
                                                  padding:
                                                  const EdgeInsets.symmetric(
                                                      vertical: 10,
                                                      horizontal: 15),
                                                  decoration: BoxDecoration(
                                                    color: message['sender'] ==
                                                        'answer' ||
                                                        message['sender'] ==
                                                            'endAnswer' ||
                                                        message['sender'] ==
                                                            'admin'
                                                        ? Colors.grey[300]
                                                        : Colors.blue,
                                                    borderRadius:
                                                    BorderRadius.circular(10),
                                                  ),
                                                  child: Column(
                                                    crossAxisAlignment:
                                                    CrossAxisAlignment.end,
                                                    children: [
                                                      Text(
                                                        message['message']!,
                                                        style: TextStyle(
                                                          color: message['sender'] ==
                                                              'answer' ||
                                                              message['sender'] ==
                                                                  'endAnswer' ||
                                                              message['sender'] ==
                                                                  'admin'
                                                              ? Colors.black
                                                              : Colors.white,
                                                        ),
                                                      ),
                                                      const SizedBox(height: 5),
                                                      Text(
                                                        message['created_at'] ??
                                                            "",
                                                        // Replace with dynamic time if needed
                                                        style: TextStyle(
                                                          fontSize: 10,
                                                          color: message['sender'] ==
                                                              'answer' ||
                                                              message['sender'] ==
                                                                  'endAnswer' ||
                                                              message['sender'] ==
                                                                  'admin'
                                                              ? Colors.black54
                                                              : Colors.white70,
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                  if (message['sender'] == 'answer' &&
                                      message['message'] != 'Sure')
                                    Padding(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 60.0),
                                      child: Row(
                                        children: [
                                          const Text(
                                              'Are this answer satisfied for you?'),
                                          5.horizontalSpace,
                                          InkWell(
                                            onTap: isLastMessage == true || isAnimationVisible == false
                                                ? () =>
                                                _sendMessage(message: {
                                                  'sender': "endAnswer",
                                                  "message":
                                                  'Great! we’re happy to help, is there any questions else?',
                                                })
                                                : null,
                                            child: Image.asset(
                                              'assets/images/Facebook Like (1).png',
                                              height: 20,
                                              width: 20,
                                            ),
                                          ),
                                          2.horizontalSpace,
                                          InkWell(
                                            onTap: isLastMessage == true || isAnimationVisible == false
                                                ? () async {
                                              await _sendMessage(message: {
                                                'sender': "endAnswer",
                                                "message":
                                                'Sorry! Where’s your problem?',
                                              });
                                              if (instance
                                                  .get<ChatCubit>()
                                                  .chatBotMessages
                                                  .any((e) =>
                                              e.question ==
                                                  'i need human')) {
                                                isQuesPreview = true;
                                                setState(() {});
                                                return;
                                              }
                                              instance
                                                  .get<ChatCubit>()
                                                  .chatBotMessages
                                                  .add(Result(
                                                  id: -1,
                                                  question:
                                                  'i need human',
                                                  answer: 'Sure'));
                                              setState(() =>
                                              isQuesPreview = true);
                                            }
                                                : null,
                                            child: Image.asset(
                                              'assets/images/Thumbs Down (1).png',
                                              height: 20,
                                              width: 20,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  if (message['sender'] == 'endAnswer' &&
                                      message['message'] ==
                                          'Great! we’re happy to help, is there any questions else?')
                                    Padding(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 30.0, vertical: 10.0),
                                      child: Row(
                                        children: [
                                          CustomButton(
                                            text: 'yes'.tr,
                                            onPressed: isLastMessage == true || isAnimationVisible == false
                                                ? () async {
                                              setState(() =>
                                              isQuesPreview = true);
                                              await _sendMessage(message: {
                                                'sender': "sender",
                                                "message": 'Yes',
                                              });
                                              Future.delayed(
                                                  const Duration(
                                                      milliseconds: 100),
                                                      () =>
                                                      _sendMessage(
                                                          message: {
                                                            'sender':
                                                            "endAnswer",
                                                            "message":
                                                            'Choose another question!',
                                                          }));
                                            }
                                                : () {},
                                            height: 40.h,
                                            radius: 8.0,
                                            color: Constants.lightGray,
                                            textColor: Constants.darkColor,
                                          ),
                                          2.horizontalSpace,
                                          CustomButton(
                                            text: 'no'.tr,
                                            onPressed: isLastMessage == true || isAnimationVisible == false
                                                ? () async {
                                              await _sendMessage(message: {
                                                'sender': "sender",
                                                "message": 'No',
                                              });
                                              Future.delayed(
                                                  const Duration(
                                                      milliseconds: 100),
                                                      () =>
                                                      _sendMessage(
                                                          message: {
                                                            'sender':
                                                            "endAnswer",
                                                            "message":
                                                            'Great! we’re happy to help, see you!',
                                                          }));
                                            }
                                                : () {},
                                            height: 40.h,
                                            radius: 8.0,
                                            color: Constants.lightGray,
                                            textColor: Constants.darkColor,
                                          ),
                                        ],
                                      ),
                                    ),
                                ],
                              );
                            },
                          ),
                        ),
                        5.verticalSpace,
                        if (isAnimationVisible == false && !isQuesPreview)
                          Align(alignment: Alignment.centerLeft,
                            child: Image.asset(
                                'assets/images/Animation - 1739286183488 1.png'),
                          ),
                        if (isQuesPreview) ...[
                          BlocProvider(
                            create: (context) => instance.get<ChatCubit>(),
                            child: BlocConsumer<ChatCubit, ChatState>(
                              listener: (_, __) {},
                              builder: (context, _) {
                                return SingleChildScrollView(
                                  scrollDirection: Axis.horizontal,
                                  child: SizedBox(
                                    height:
                                    MediaQuery
                                        .of(context)
                                        .size
                                        .height * 0.1,
                                    width: MediaQuery
                                        .of(context)
                                        .size
                                        .width,
                                    child: ListView.builder(
                                      scrollDirection: Axis.horizontal,
                                      itemCount: instance
                                          .get<ChatCubit>()
                                          .chatBotMessages
                                          .length,
                                      itemBuilder: (context, index) {
                                        final question = instance
                                            .get<ChatCubit>()
                                            .chatBotMessages[index];
                                        final screenWidth =
                                            MediaQuery
                                                .of(context)
                                                .size
                                                .width;
                                        final itemWidth =
                                            (screenWidth - (18.0 * 4)) /
                                                1; // Adjust for spacing
                                        return Padding(
                                          padding: const EdgeInsets.all(8.0),
                                          child: GestureDetector(
                                            onTap: () {
                                              if (question.id == -1) {
                                                Future.delayed(
                                                    const Duration(
                                                        milliseconds: 900), () {
                                                  isBot = false;
                                                  setState(() {});
                                                });
                                                isAnimationVisible = false;
                                                setState(() {});
                                              }
                                              setState(() {
                                                isAnimationVisible =
                                                false; // Hide the animation image
                                                _sendMessage(message: {
                                                  'sender': "ques",
                                                  "message": question.question,
                                                });
                                                Future.delayed(
                                                    const Duration(
                                                        milliseconds: 100), () {
                                                  _sendMessage(message: {
                                                    'sender': "answer",
                                                    "message": question.answer,
                                                  });
                                                });
                                                isQuesPreview = false;
                                                isBotAnswerLoading = true;
                                              });
                                            },
                                            child: Container(
                                              width: SizeConfig.widthr(
                                                  itemWidth, context),
                                              height:
                                              SizeConfig.hieghtr(44, context),
                                              decoration: ShapeDecoration(
                                                gradient: Constants.defGradient,
                                                shape: RoundedRectangleBorder(
                                                    borderRadius:
                                                    BorderRadius.circular(
                                                        52)),
                                              ),
                                              child: Padding(
                                                padding: const EdgeInsets.symmetric(horizontal: 5.0),
                                                child: Center(
                                                  child: Text(
                                                    question.question ?? "",
                                                    style: TextStyle(
                                                        fontSize: 15.0.sp,
                                                        color: Colors.white),
                                                    textAlign: TextAlign.center,
                                                  ),
                                                ),
                                              ),
                                              // radius: 8.0,
                                              // color: Constants.mainColor,
                                            ),
                                          ),
                                        );
                                      },
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),
                          SizedBox(height: 20.h),
                        ]

                        // 30.verticalSpace,
                      ],
                    );
                  }
                  return const SizedBox();
                },
              ),
            ),
            SizedBox(height: 20.h),
            BlocBuilder<ChatCubit, ChatState>(
              builder: (context, state) {
                final chatCubit = context.watch<ChatCubit>();
                return isBot == false
                    ? Row(
                  children: [
                    Expanded(
                      child: TextField(
                        controller: _controller,
                        focusNode: keyNode,
                        textInputAction: TextInputAction.send,
                        decoration: InputDecoration(
                            labelText: 'Type a message...'.tr,
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(20),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(20),
                              borderSide: const BorderSide(
                                  color: Colors.blue, width: 2.0),
                            ),
                            fillColor: AppColors.darkColor,
                            labelStyle:
                            const TextStyle(color: AppColors.greyText)),
                        onSubmitted: (_) {
                          _sendMessage();
                          setState(() {});
                        },
                      ),
                    ),
                    IconButton(
                      icon: const Icon(
                        Icons.send,
                        color:
                        // _controller.text.isNotEmpty
                        //     ?
                        AppColors.darkColor,
                        // : Colors.grey,
                      ),
                      onPressed: () async {
                        print("chatCampaignIssues ${ChatCubit.get(context).isConnected}");
                        if (ChatCubit
                            .get(context)
                            .isConnected == false) {
                          await context
                              .read<ChatCubit>()
                              .pusherConfiguration(context);
                        }
                        await _sendMessage();
                        isBot = false;
                        // ChatCubit
                        //     .get(context)
                        //     .isConnected = true;
                        // context.read<ChatCubit>().toggleIsBot(false);
                        setState(() {
                          // ChatCubit.get(context).isConnected = true;
                        });
                      },
                    ),
                  ],
                )
                    : const SizedBox();
              },
            ),
            30.verticalSpace,
          ],
        ),
      ),
    );
  }
}
